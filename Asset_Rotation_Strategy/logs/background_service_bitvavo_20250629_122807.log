2025-06-29 12:28:07,287 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - Exchange-specific logging initialized for bitvavo
2025-06-29 12:28:07,527 - [B<PERSON><PERSON><PERSON>] - root - INFO - Telegram command handlers registered
2025-06-29 12:28:07,528 - [<PERSON><PERSON><PERSON><PERSON>] - root - ERROR - Error in Telegram polling: set_wakeup_fd only works in main thread of the main interpreter
2025-06-29 12:28:07,528 - [BITVAVO] - root - INFO - Telegram bot polling started
2025-06-29 12:28:07,529 - [BIT<PERSON><PERSON>] - root - WARNING - Continuing without Telegram bot polling. Notifications will still be sent.
2025-06-29 12:28:07,529 - [B<PERSON><PERSON><PERSON>] - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-29 12:28:07,530 - [BITVAVO] - root - INFO - Telegram notification channel initialized
2025-06-29 12:28:07,532 - [<PERSON>ITVAVO] - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-29 12:28:07,532 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - <PERSON>aded 24 templates from file
2025-06-29 12:28:07,532 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - Notification manager initialized with 1 channels
2025-06-29 12:28:07,532 - [BITVAVO] - root - INFO - Notification manager initialized
2025-06-29 12:28:07,532 - [BITVAVO] - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-29 12:28:07,533 - [BITVAVO] - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-29 12:28:07,533 - [BITVAVO] - root - INFO - Set up critical time windows for 1d timeframe
2025-06-29 12:28:07,533 - [BITVAVO] - root - INFO - Network watchdog initialized with 10s check interval
2025-06-29 12:28:07,533 - [BITVAVO] - root - INFO - Loaded recovery state from data/state/recovery_state.json
2025-06-29 12:28:07,534 - [BITVAVO] - root - INFO - No state file found at /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/data/state/data/state/background_service_20250627_214259.json.json
2025-06-29 12:28:07,534 - [BITVAVO] - root - INFO - Recovery manager initialized
2025-06-29 12:28:07,534 - [BITVAVO] - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-06-29 12:28:07,534 - [BITVAVO] - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'live', 'order_type': 'market', 'position_size_pct': 99.99, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025}
2025-06-29 12:28:07,534 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 12:28:07,549 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-06-29 12:28:07,550 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-29 12:28:07,550 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-29 12:28:07,550 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-29 12:28:07,550 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-29 12:28:07,550 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-29 12:28:07,550 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-29 12:28:07,550 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-29 12:28:07,550 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-29 12:28:07,550 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-29 12:28:07,562 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-06-29 12:28:08,098 - [BITVAVO] - root - INFO - Successfully loaded markets for bitvavo.
2025-06-29 12:28:08,149 - [BITVAVO] - root - ERROR - Error initializing exchange: bitvavo {"errorCode":307,"error":"This key does not allow access from this IP."}
2025-06-29 12:28:08,149 - [BITVAVO] - root - ERROR - Error initializing components: bitvavo {"errorCode":307,"error":"This key does not allow access from this IP."}
2025-06-29 12:28:08,150 - [BITVAVO] - root - ERROR - Error in main thread: bitvavo {"errorCode":307,"error":"This key does not allow access from this IP."}
Traceback (most recent call last):
  File "/home/<USER>/asset_rotation_screener/venv/lib/python3.12/site-packages/ccxt/base/exchange.py", line 581, in fetch
    response.raise_for_status()
  File "/home/<USER>/asset_rotation_screener/venv/lib/python3.12/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 403 Client Error: Forbidden for url: https://api.bitvavo.com/v2/balance

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/background_service.py", line 2727, in main
    service = BackgroundService(args.config, notification_config_path=args.notifications, test_mode=args.test)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/background_service.py", line 105, in __init__
    self.initialize_components()
  File "/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/background_service.py", line 237, in initialize_components
    self.trading_executor = TradingExecutor(exchange_id, test_mode=self.test_mode, config_path=self.config_path)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/src/trading/executor.py", line 60, in __init__
    self.account_manager = AccountManager(exchange_id, test_mode=test_mode, config_path=config_path)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/src/trading/account.py", line 44, in __init__
    self._initialize_exchange()
  File "/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/src/trading/account.py", line 127, in _initialize_exchange
    self.exchange.fetch_balance()
  File "/home/<USER>/asset_rotation_screener/venv/lib/python3.12/site-packages/ccxt/bitvavo.py", line 1079, in fetch_balance
    response = self.privateGetBalance(params)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/asset_rotation_screener/venv/lib/python3.12/site-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/asset_rotation_screener/venv/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4468, in request
    return self.fetch2(path, api, method, params, headers, body, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/asset_rotation_screener/venv/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4464, in fetch2
    raise e
  File "/home/<USER>/asset_rotation_screener/venv/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4453, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/asset_rotation_screener/venv/lib/python3.12/site-packages/ccxt/base/exchange.py", line 597, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/asset_rotation_screener/venv/lib/python3.12/site-packages/ccxt/bitvavo.py", line 2085, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], errorCode, feedback)
  File "/home/<USER>/asset_rotation_screener/venv/lib/python3.12/site-packages/ccxt/base/exchange.py", line 4857, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.PermissionDenied: bitvavo {"errorCode":307,"error":"This key does not allow access from this IP."}
