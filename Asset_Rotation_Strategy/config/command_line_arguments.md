python main_program.py --assets BTC/USDT ETH/USDT --trend-method "PGO For Loop" --weighted --weights 0.8 0.2 --timeframe 1d --analysis-start-date 2023-10-19 --n-assets 2 --mtpi-combination-method consensus --mtpi-long-threshold 0.1 --mtpi-short-threshold -0.1 --mtpi-indicators pgo bollinger_bands dwma_score dema_super_score dpsd_score aad_score dynamic_ema_score quantile_dema_score 

to clean the cache (from potential duplicated)

../venv/bin/python run_cache_cleanup.py


--ratio-calculation independent  ## manual_inversion


--execution-timing manual_12pm ## candle_close

--analysis-end-date 2025-06-20

sudo systemctl list-unit-files | grep asset_rotation
good function for checking whats loaded and whats not


meme backtest

python main_program.py --include-autism --include-fwog --include-toshi --include-altura --include-sigma --include-lockin --include-spx --include-ski --include-chillguy --include-apu --include-selfie --include-alienbase --include-dogeeth --include-michi --include-mini --include-nub --include-popcat --include-giko --include-coby --include-giga --include-aura --include-cocoro --include-skibidi --include-dolan --include-usa --include-epik  --n-assets 1 --timeframe 1d --mtpi-timeframe 12h  --analysis-start-date 2024-05-07 --assets BTC/USDT --trend-method "PGO For Loop" --mtpi-combination-method consensus --mtpi-long-threshold 0.1 --mtpi-short-threshold -0.1 --mtpi-indicators pgo bollinger_bands dwma_score dema_super_score dpsd_score aad_score dynamic_ema_score quantile_dema_score --ratio-calculation independent --tie-breaking-strategy momentum


python background_service.py --config config/settings_bitvavo_eur.yaml --notifications config/notifications_bitvavo.json 

api_host: 0.0.0.0
api_port: 5001
assets:
- BTC/USDC
- ETH/USDC
- SOL/USDC
- SUI/USDC
exchange: binance
initial_capital: 10000
rebalance_frequency: W
scoring_metric: EMA_50
settings:
  aad_period: 14
  assets:
  - BTC/USDC
  - ETH/USDC
  - SOL/USDC
  - SUI/USDC
  - XRP/USDC
  cci_length: 20
  dmi_length: 14
  enable_rebalancing: false
  exchange: binance
  initial_capital: 10000
  mtpi_indicator_type: PGO
  mtpi_lower_threshold: -0.58
  mtpi_pgo_length: 35
  mtpi_timeframe: 1d
  mtpi_upper_threshold: 1.1
  n_assets: 2
  pgo_length: 35
  pgo_lower_threshold: -0.58
  pgo_upper_threshold: 1.1
  plot_bah: true
  plot_metric_table: true
  rebalance_frequency: W
  rebalance_threshold: 0.05
  rsi_length: 14
  rsi_ma_length: 14
  scoring_metric: EMA_50
  start_date: '2025-02-10'
  supertrend_factor: 3
  timeframe: 1d
  transaction_fee_rate: 0.001
  trend_method: PGO For Loop
  use_mtpi_signal: true
  use_weighted_allocation: true
  weights:
  - 0.8
  - 0.2
  # MTPI Multi-Indicator Configuration
  # Simply comment/uncomment indicators you want to enable/disable
  mtpi_indicators:
    enabled_indicators:
      - pgo
      - bollinger_bands
      - dwma_score          # Uncomment to enable
      - dema_super_score    # Uncomment to enable
      - dpsd_score
      - aad_score
      - dynamic_ema_score      # Uncomment to enable
      - quantile_dema_score 
      # - median_score    # Quantile DEMA Trend indicator

    combination_method: consensus  # consensus, majority, weighted_consensus

    # Signal aggregation thresholds (crossover/crossunder behavior like PineScript)
    long_threshold: 0.1      # Crossover above this = Long signal
    short_threshold: -0.1   # Crossunder below this = Short signal

    # Individual indicator parameters (single source of truth)
    pgo:
      length: 35
      upper_threshold: 1.1
      lower_threshold: -0.58

    bollinger_bands:
      length: 33
      multiplier: 2.0
      long_threshold: 76.0
      short_threshold: 31.0
      use_heikin_ashi: false
      heikin_src: 'close'

    dwma_score:
      smoothing_style: 'Weighted SD'  # ATR, Weighted SD, For loop (matching TradingView)
      src_col: 'close'
      length: 17                      # DWA Length (matching TradingView)
      ma_type: 'EMA'                  # Smoothing MA
      ma_smooth_length: 12            # MA Smooth
      # Weighted SD parameters (matching TradingView)
      sd_length: 33
      upper_sd_weight: 1.031
      lower_sd_weight: 0.996
      # ATR parameters (matching TradingView)
      atr_period: 12
      atr_multiplier: 1.0
      # For loop parameters (matching TradingView)
      loop_start: 1
      loop_end: 60
      long_threshold: 30
      short_threshold: 0

    median_score:
      atr_period: 12          # subject1 - Supertrend len (matching PineScript)
      multiplier: 1.45        # mul1 - Multiplier (matching PineScript)
      median_length: 27       # slen - Median len (matching PineScript)
      src_col: 'high'         # src_me - Median smoothing source (matching PineScript)

    dema_super_score:
      atr_period: 19
      multiplier: 2.8
      length: 17
      src_col: 'close'

    dpsd_score:
      dema_length: 9
      percentile_length: 58
      sd_length: 27
      ema_length: 14
      percentile_upper: 60.0
      percentile_lower: 45.0
      src_col: 'high'

    aad_score:
      src_col: 'close'        # Source for AAD calculation (matching PineScript)
      length: 22              # AAD calculation length (matching PineScript)
      aad_mult: 1.2           # AAD multiplier for band calculation (matching PineScript)
      avg_type: 'SMA'         # Average type for AAD calculation (matching PineScript)

    dynamic_ema_score:
      median_length: 9        # Median calculation length (matching PineScript)
      median_src: 'close'     # Source for median calculation (matching PineScript)
      ema_length: 12          # Dynamic EMA calculation length (matching PineScript)
      smoothing_style: 'Weighted SD'  # Smoothing style: 'ATR' or 'Weighted SD' (matching PineScript)
      sd_length: 33           # Standard deviation length (matching PineScript)
      upper_sd_weight: 1.017  # Upper SD weight (matching PineScript)
      lower_sd_weight: 0.996  # Lower SD weight (matching PineScript)
      atr_period: 14          # ATR period (matching PineScript)
      atr_multiplier: 1.2     # ATR multiplier (matching PineScript)

    quantile_dema_score:
      dema_length: 30         # DEMA Length (matching PineScript)
      percentile_filter: 10   # Percentile Filter (matching PineScript)
      atr_length: 14          # ATR Length (matching PineScript)
      mult_up: 1.2           # Long Multiplier (matching PineScript)
      mult_dn: 1.2           # Short Multiplier (matching PineScript)
      dema_st_length: 30      # DEMA Supertrend Length (matching PineScript)
      percentile_length: 20   # Percentile Filter length (matching PineScript)
      sd_length: 30          # SD Length (matching PineScript)
      src_col: 'close'       # Source column for calculations
      
start_date: 2025-02-10
timeframe: 1d
trading:
  enabled: true
  initial_capital: 100
  max_slippage_pct: 0.5
  mode: paper
  order_type: market
  position_size_pct: 99.99999
  risk_management:
    max_daily_trades: 5
    max_open_positions: 10

asset trend method options

RSI - Uses RSI-based trend detection
PGO - Alias for "PGO For Loop" (uses PGO indicator)
PGO For Loop - Uses PGO (Price Growth Oscillator) with pairwise comparisons
MTPI - Uses Multi-Technical Performance Indicator aggregation